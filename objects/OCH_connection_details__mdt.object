<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <fields>
        <fullName>AWS_Password__c</fullName>
        <description>CDCS-184 password used to authenticate on AWS statement generator</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Password used to authenticate on AWS statement generator</inlineHelpText>
        <label>AWS Password</label>
        <length>100</length>
        <required>false</required>
        <type>EncryptedText</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>AWS_STatement_Auth_Path__c</fullName>
        <description>CDCS-184 path to user for AWS statement generator request</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Path to user for AWS statement generator request</inlineHelpText>
        <label>AWS STatement Auth Path</label>
        <length>100</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>AWS_Service_Timeout__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>AWS Service Timeout</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>AWS_Statement_Path__c</fullName>
        <description>CDCS-184 Request path for generating statement</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>AWS Statement Path</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>AWS_Username__c</fullName>
        <description>CDCS-184 - username used to authenticate on AWS statement generator</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Username used to authenticate on AWS statement generator</inlineHelpText>
        <label>AWS Username</label>
        <length>100</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Account_Details_Path__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Account Details Path</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Account_Search_Path__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Account Search Path</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Auth_Path__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Auth Path</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Balances_Path__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Balances Path</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Bank_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Bank Id</label>
        <length>100</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Channel_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Channel Id</label>
        <length>100</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Client_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Client Id</label>
        <length>100</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Client_Secret__c</fullName>
        <description>OAuth client secret for API authentication - encrypted for security</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>OAuth client secret for API authentication</inlineHelpText>
        <label>Client Secret</label>
        <length>255</length>
        <required>true</required>
        <type>EncryptedText</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Corp_Principal__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Corp Principal</label>
        <length>10</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Language_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Language Id</label>
        <length>10</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Login_Flag__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Login Flag</label>
        <length>10</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Login_Type__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Login Type</label>
        <length>10</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Named_Credential__c</fullName>
        <description>CDCS-144 - Named credential to use when making requests to OCH api</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Named credential to use when making requests to OCH api</inlineHelpText>
        <label>Named Credential</label>
        <length>20</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>OCH_Service_Timeout__c</fullName>
        <description>OCH service timeout</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>OCH Service Timeout</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Password__c</fullName>
        <description>User password for OCH API authentication - encrypted for security</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>User password for OCH API authentication</inlineHelpText>
        <label>Password</label>
        <length>255</length>
        <required>true</required>
        <type>EncryptedText</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Statement_Inquiry_Path__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Statement Inquiry Path</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Token_Key__c</fullName>
        <description>Encryption key for token access - encrypted for security</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Encryption key for token access</inlineHelpText>
        <label>Token Key</label>
        <length>100</length>
        <required>false</required>
        <type>EncryptedText</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>User_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>User Id</label>
        <length>40</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Username__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Username</label>
        <length>100</length>
        <required>true</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Credential_Expiry_Date__c</fullName>
        <description>Date when credentials expire and need to be rotated</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Date when credentials expire and need to be rotated</inlineHelpText>
        <label>Credential Expiry Date</label>
        <required>false</required>
        <type>Date</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Last_Credential_Update__c</fullName>
        <description>Date when credentials were last updated</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Date when credentials were last updated</inlineHelpText>
        <label>Last Credential Update</label>
        <required>false</required>
        <type>DateTime</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Credential_Version__c</fullName>
        <description>Version number for credential rotation tracking</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Version number for credential rotation tracking</inlineHelpText>
        <label>Credential Version</label>
        <precision>3</precision>
        <required>false</required>
        <scale>0</scale>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <label>OCH connection details</label>
    <pluralLabel>OCH connections details</pluralLabel>
    <visibility>Public</visibility>
</CustomObject>
