# OCH Security Improvements - Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the OCH connection security improvements that replace direct credential storage with Salesforce Named Credentials.

## Pre-Deployment Checklist

### 1. Backup Current Configuration
- [ ] Export current OCH connection metadata records
- [ ] Document current credential values (store securely)
- [ ] Test current OCH functionality to establish baseline

### 2. Environment Preparation
- [ ] Ensure Salesforce org has Named Credentials feature enabled
- [ ] Verify deployment user has appropriate permissions:
  - Modify All Data
  - Manage Named Credentials
  - Deploy Metadata

## Deployment Steps

### Phase 1: Deploy Core Components

#### 1.1 Deploy External Credential
```bash
# Deploy the External Credential definition
sfdx force:source:deploy -p externalCredentials/OCH_External_Credential.externalCredential
```

#### 1.2 Deploy Named Credentials
```bash
# Deploy country-specific Named Credentials
sfdx force:source:deploy -p namedCredentials/OCH_API_Zambia.namedCredential
sfdx force:source:deploy -p namedCredentials/OCH_API_Mauritius.namedCredential
```

#### 1.3 Deploy Updated Custom Object
```bash
# Deploy updated OCH connection details metadata type
sfdx force:source:deploy -p objects/OCH_connection_details__mdt.object
```

### Phase 2: Deploy Apex Classes

#### 2.1 Deploy Helper Classes
```bash
# Deploy credential management utilities
sfdx force:source:deploy -p classes/OSB_SRV_OCHCredentialHelper.cls
sfdx force:source:deploy -p classes/OSB_SRV_OCHCredentialHelper.cls-meta.xml

# Deploy audit logging service
sfdx force:source:deploy -p classes/OSB_SRV_OCHAuditLogger.cls
sfdx force:source:deploy -p classes/OSB_SRV_OCHAuditLogger.cls-meta.xml

# Deploy credential rotation service
sfdx force:source:deploy -p classes/OSB_SRV_OCHCredentialRotation.cls
sfdx force:source:deploy -p classes/OSB_SRV_OCHCredentialRotation.cls-meta.xml

# Deploy monitoring scheduler
sfdx force:source:deploy -p classes/OSB_SCH_OCHCredentialMonitor.cls
sfdx force:source:deploy -p classes/OSB_SCH_OCHCredentialMonitor.cls-meta.xml
```

#### 2.2 Deploy Updated Existing Classes
```bash
# Deploy updated request factory
sfdx force:source:deploy -p classes/OSB_VA_RequestFactory.cls

# Deploy updated authentication controller
sfdx force:source:deploy -p classes/OSB_VA_OCHAuthenticate_CTRL.cls
```

#### 2.3 Deploy Test Classes
```bash
# Deploy test classes
sfdx force:source:deploy -p classes/OSB_SRV_OCHCredentialHelper_TEST.cls
sfdx force:source:deploy -p classes/OSB_SRV_OCHCredentialHelper_TEST.cls-meta.xml
```

### Phase 3: Configure Named Credentials

#### 3.1 Configure External Credential Parameters
1. Navigate to Setup > Named Credentials > External Credentials
2. Open "OCH External Credential"
3. Configure parameter values:

**OAuth Client Credentials:**
- Client Id: [Your OCH Client ID]
- Client Secret: [Your OCH Client Secret]

**User Credentials:**
- Username: [Your OCH Username]
- Password: [Your OCH Password]

**AWS Credentials:**
- AWS Username: [Your AWS Username]
- AWS Password: [Your AWS Password]

**Encryption:**
- Token Key: [Your Token Key]

#### 3.2 Configure Named Credential Principals
For each Named Credential (OCH_API_Zambia, OCH_API_Mauritius):

1. Navigate to Setup > Named Credentials
2. Open the Named Credential
3. Configure Principal settings:
   - Principal Type: Named Principal
   - Authentication Protocol: OAuth
   - Generate Authorization Header: Yes
4. Map External Credential parameters to appropriate values

### Phase 4: Update Custom Metadata

#### 4.1 Deploy Updated Metadata Records
```bash
# Deploy updated connection records
sfdx force:source:deploy -p customMetadata/OCH_connection_details.OCH_Zambia.md
sfdx force:source:deploy -p customMetadata/OCH_connection_details.OCH_Mauritius.md
sfdx force:source:deploy -p customMetadata/OCH_connection_details.OCH_connection.md
```

### Phase 5: Post-Deployment Configuration

#### 5.1 Schedule Credential Monitoring
Execute in Anonymous Apex:
```apex
// Schedule daily credential monitoring at 6 AM
String jobId = OSB_SCH_OCHCredentialMonitor.scheduleJob('0 0 6 * * ?');
System.debug('Scheduled OCH Credential Monitor with Job ID: ' + jobId);
```

#### 5.2 Set Initial Credential Expiry Dates
Update custom metadata records with appropriate expiry dates:
1. Navigate to Setup > Custom Metadata Types > OCH connection details
2. For each record, set:
   - Credential_Expiry_Date__c: 90 days from deployment
   - Last_Credential_Update__c: Current date/time
   - Credential_Version__c: 1

## Testing and Validation

### 1. Functional Testing
- [ ] Test OCH authentication for each country
- [ ] Verify API calls work with Named Credentials
- [ ] Test AWS statement generation functionality
- [ ] Validate error handling and logging

### 2. Security Testing
- [ ] Verify credentials are not visible in debug logs
- [ ] Confirm audit logs are being created
- [ ] Test credential rotation functionality
- [ ] Validate monitoring and alerting

### 3. Performance Testing
- [ ] Compare response times before/after changes
- [ ] Monitor system performance under load
- [ ] Verify no degradation in user experience

## Rollback Plan

### If Issues Occur:
1. **Immediate Rollback:**
   ```bash
   # Revert to previous versions
   sfdx force:source:deploy -p classes/OSB_VA_RequestFactory.cls --rollback
   sfdx force:source:deploy -p classes/OSB_VA_OCHAuthenticate_CTRL.cls --rollback
   ```

2. **Restore Original Metadata:**
   - Restore backed-up custom metadata records
   - Temporarily re-enable old credential fields if needed

3. **Emergency Access:**
   - Keep backup of original credentials in secure location
   - Have emergency contact information for credential providers

## Monitoring and Maintenance

### 1. Daily Monitoring
- Review audit logs for unusual activity
- Check credential expiry alerts
- Monitor system performance metrics

### 2. Weekly Reviews
- Analyze authentication failure patterns
- Review security violation logs
- Update credential rotation schedules as needed

### 3. Monthly Tasks
- Review and update Named Credential configurations
- Audit user access to credential management
- Update documentation and procedures

## Security Best Practices

### 1. Access Control
- Limit Named Credential access to authorized personnel only
- Use principle of least privilege for credential management
- Regular review of user permissions

### 2. Credential Management
- Rotate credentials every 90 days minimum
- Use strong, unique passwords for each environment
- Implement multi-factor authentication where possible

### 3. Monitoring and Alerting
- Set up real-time alerts for authentication failures
- Monitor for unusual access patterns
- Regular security audits and penetration testing

## Troubleshooting

### Common Issues and Solutions

#### 1. Authentication Failures
**Symptoms:** API calls returning 401/403 errors
**Solutions:**
- Verify Named Credential configuration
- Check External Credential parameter values
- Review audit logs for specific error details

#### 2. Missing Audit Logs
**Symptoms:** No audit entries being created
**Solutions:**
- Verify Log__c object permissions
- Check Apex class execution permissions
- Review debug logs for logging errors

#### 3. Credential Rotation Issues
**Symptoms:** Rotation process failing
**Solutions:**
- Verify Metadata API permissions
- Check External Credential parameter updates
- Review rotation service logs

## Support and Contacts

### Technical Support
- **Primary Contact:** [Your Technical Lead]
- **Secondary Contact:** [Your System Administrator]
- **Emergency Contact:** [24/7 Support Number]

### Vendor Contacts
- **OCH API Support:** [OCH Support Contact]
- **AWS Support:** [AWS Support Contact]
- **Salesforce Support:** [Salesforce Support Case Portal]

## Conclusion

This deployment guide ensures a secure and systematic migration from direct credential storage to Named Credentials. Following these steps will significantly enhance the security posture of your OCH integration while maintaining functionality and improving manageability.

Remember to:
- Test thoroughly in sandbox before production deployment
- Maintain secure backups of all credentials
- Monitor system behavior closely after deployment
- Keep this documentation updated with any changes
