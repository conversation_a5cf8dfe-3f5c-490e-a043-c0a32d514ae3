# OCH Named Credentials Setup Guide
## Based on Official Salesforce Documentation

This guide provides step-by-step instructions for implementing Named Credentials and External Credentials for OCH connections, sourced directly from official Salesforce documentation.

## Overview

Named Credentials in Salesforce provide a secure way to store authentication details for external services. They work in conjunction with External Credentials to manage authentication protocols and store sensitive information securely.

**Key Benefits:**
- Secure storage of credentials (encrypted at rest)
- Simplified callout setup
- No hardcoded credentials in code
- Centralized credential management
- Support for multiple authentication protocols

## Prerequisites

Before starting, ensure you have:
- System Administrator or equivalent permissions
- Access to Setup in your Salesforce org
- OCH API credentials (client ID, client secret, username, password)
- Understanding of your OCH API authentication requirements

## Step 1: Understand Authentication Protocols

External Credentials support several authentication protocols:

1. **No Authentication** - For public APIs
2. **Custom** - For API keys in headers
3. **OAuth 2.0** - Multiple flows:
   - Browser Flow (Per User)
   - Client Credentials Flow
   - JWT Bearer Flow
4. **AWS Signature Version 4** - For AWS services

For OCH, we'll likely use **OAuth 2.0 Client Credentials Flow** or **Custom** authentication.

## Step 2: Create External Credential

### 2.1 Navigate to External Credentials
1. In Setup, enter `External Credentials` in the Quick Find box
2. Select **External Credentials**
3. Click **New**

### 2.2 Configure External Credential
**For OAuth 2.0 Client Credentials Flow:**

1. **Label**: `OCH External Credential`
2. **Name**: `OCH_External_Credential` (auto-populated)
3. **Authentication Protocol**: Select `OAuth 2.0`
4. **OAuth Flow Type**: Select `Client Credentials`
5. **Token Endpoint URL**: Enter your OCH OAuth token endpoint
   - Example: `https://salesforce.ohc.standardbank.co.za/corp/oAuth/token`
6. **Scope**: Enter required scopes (if any)
7. **Client Authentication Method**: Select `HTTP Basic Authentication`

**For Custom Authentication (API Key):**

1. **Label**: `OCH External Credential`
2. **Name**: `OCH_External_Credential`
3. **Authentication Protocol**: Select `Custom`

### 2.3 Add External Credential Parameters

Click **New** to add parameters:

**For OAuth 2.0:**
- **Parameter 1:**
  - Parameter Name: `client_id`
  - Parameter Type: `NamedPrincipal`
- **Parameter 2:**
  - Parameter Name: `client_secret`
  - Parameter Type: `NamedPrincipal`

**For Custom Authentication:**
- **Parameter 1:**
  - Parameter Name: `username`
  - Parameter Type: `NamedPrincipal`
- **Parameter 2:**
  - Parameter Name: `password`
  - Parameter Type: `NamedPrincipal`

### 2.4 Save External Credential
Click **Save** to create the External Credential.

## Step 3: Create Named Credential

### 3.1 Navigate to Named Credentials
1. In Setup, enter `Named Credentials` in the Quick Find box
2. Select **Named Credentials**
3. Click **New**

### 3.2 Configure Named Credential
1. **Label**: `OCH API`
2. **Name**: `OCH_API` (auto-populated)
3. **URL**: Enter your OCH base URL
   - Example: `https://salesforce.ohc.standardbank.co.za`
4. **External Credential**: Select `OCH External Credential`
5. **Enabled for Callouts**: Check this box
6. **Generate Authorization Header**: Check this box (for OAuth)

### 3.3 Advanced Settings
1. **Allow Merge Fields in HTTP Header**: Check if needed
2. **Allow Merge Fields in HTTP Body**: Check if needed
3. **Outbound Network Connection**: Leave default unless specific requirements

### 3.4 Save Named Credential
Click **Save** to create the Named Credential.

## Step 4: Configure External Credential Principals

### 4.1 Navigate Back to External Credentials
1. Go to Setup → External Credentials
2. Find your `OCH External Credential`
3. Click on it to open

### 4.2 Create Principal
1. Click **New** in the Principals section
2. **Principal Name**: `OCH_Principal`
3. **Principal Type**: `NamedPrincipal`

### 4.3 Set Parameter Values
For each parameter you created:

**OAuth 2.0 Example:**
- `client_id`: Enter your actual OCH client ID
- `client_secret`: Enter your actual OCH client secret

**Custom Authentication Example:**
- `username`: Enter your OCH username
- `password`: Enter your OCH password

### 4.4 Save Principal
Click **Save** to store the principal with credentials.

## Step 5: Test the Named Credential

### 5.1 Test Connection
1. Go to Setup → Named Credentials
2. Find your `OCH API` Named Credential
3. Click **Edit**
4. Click **Test** button (if available)

### 5.2 Test with Apex
Create a simple test class:

```apex
public class OCHNamedCredentialTest {
    public static void testConnection() {
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:OCH_API/corp/oAuth/token');
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        // Add required body parameters
        String body = 'grant_type=password&bank_id=YOUR_BANK_ID';
        req.setBody(body);
        
        Http http = new Http();
        HttpResponse res = http.send(req);
        
        System.debug('Status Code: ' + res.getStatusCode());
        System.debug('Response: ' + res.getBody());
    }
}
```

## Step 6: Update Your Apex Code

### 6.1 Modify OCH Request Factory
Update your `OSB_VA_RequestFactory` class to use Named Credentials:

```apex
public static HttpRequest createOCHAuthenticateRequest(String inputCountryName) {
    OCH_connection_details__mdt connectionDetails = getOCHConnectionDetails(inputCountryName);
    
    HttpRequest request = new HttpRequest();
    request.setMethod('POST');
    // Use Named Credential instead of direct URL
    request.setEndpoint('callout:OCH_API' + connectionDetails.Auth_Path__c);
    request.setHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    // Build body with non-sensitive parameters only
    String body = 'grant_type=password';
    body += '&bank_id=' + EncodingUtil.urlEncode(connectionDetails.Bank_Id__c, 'UTF-8');
    body += '&channel_id=' + EncodingUtil.urlEncode(connectionDetails.Channel_Id__c, 'UTF-8');
    // Add other non-sensitive parameters...
    
    request.setBody(body);
    request.setTimeout(Integer.valueOf(connectionDetails.OCH_Service_Timeout__c));
    
    return request;
}
```

### 6.2 Remove Sensitive Fields from Custom Metadata
Consider removing these fields from `OCH_connection_details__mdt`:
- `Client_Id__c`
- `Client_Secret__c`
- `Username__c`
- `Password__c`

These are now handled securely by Named Credentials.

## Step 7: Create Country-Specific Named Credentials (Optional)

For different countries, you can create separate Named Credentials:

### 7.1 Create OCH_API_Zambia
1. Follow steps 3.1-3.4 above
2. **Label**: `OCH API Zambia`
3. **Name**: `OCH_API_Zambia`
4. Use the same External Credential but create a separate Principal with Zambia-specific credentials

### 7.2 Create OCH_API_Mauritius
1. Follow steps 3.1-3.4 above
2. **Label**: `OCH API Mauritius`
3. **Name**: `OCH_API_Mauritius`
4. Use the same External Credential but create a separate Principal with Mauritius-specific credentials

## Step 8: Security Best Practices

### 8.1 Credential Management
- Rotate credentials regularly (every 90 days recommended)
- Use strong, unique passwords
- Limit access to Named Credentials setup
- Monitor credential usage through Setup Audit Trail

### 8.2 Access Control
- Assign appropriate permissions for Named Credential access
- Use Permission Sets to control who can view/edit credentials
- Regular review of user access

### 8.3 Monitoring
- Enable debug logs to monitor API calls
- Set up alerts for authentication failures
- Regular review of API usage patterns

## Troubleshooting

### Common Issues

**1. Authentication Failures**
- Verify External Credential parameter values
- Check Named Credential URL and paths
- Confirm OAuth flow type matches API requirements

**2. Endpoint Not Found**
- Verify Named Credential URL is correct
- Check that paths in custom metadata are accurate
- Ensure Remote Site Settings are not blocking calls

**3. Permission Errors**
- Verify user has access to Named Credential
- Check Permission Sets and Profiles
- Ensure External Credential principals are properly configured

### Debug Steps
1. Enable debug logs for your user
2. Test with simple HTTP callout
3. Check Setup Audit Trail for configuration changes
4. Verify credentials work outside Salesforce (e.g., Postman)

## Official Salesforce Documentation References

- [Create Named Credentials and External Credentials](https://help.salesforce.com/s/articleView?id=sf.nc_named_creds_and_ext_creds.htm)
- [Named Credentials as Callout Endpoints](https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_callouts_named_credentials.htm)
- [Create or Edit a Named Credential](https://help.salesforce.com/s/articleView?id=sf.nc_create_edit_named_credential.htm)
- [External Credentials](https://help.salesforce.com/s/articleView?id=sf.nc_external_credentials.htm)

## Next Steps

After implementing Named Credentials:
1. Test all OCH API functionality
2. Update any hardcoded credentials in other classes
3. Implement credential rotation procedures
4. Set up monitoring and alerting
5. Document the new authentication flow for your team

This approach provides enterprise-grade security for your OCH integration while maintaining the flexibility and functionality of your existing system.
