/**
 * @description Scheduled job for monitoring OCH credential expiry and triggering rotation
 * Runs daily to check for expiring credentials and send notifications
 * 
 * <AUTHOR> Agent
 * @date 2025
 */
public class OSB_SCH_OCHCredentialMonitor implements Schedulable {
    
    private static final String LOG_SOURCE = 'OSB_SCH_OCHCredentialMonitor';
    private static final String LOG_AREA = 'OCH_Credential_Monitoring';
    private static final Integer WARNING_DAYS = 30;
    private static final Integer CRITICAL_DAYS = 7;
    
    /**
     * @description Schedulable execute method
     * @param sc SchedulableContext
     */
    public void execute(SchedulableContext sc) {
        try {
            monitorCredentials();
        } catch (Exception e) {
            logError('execute', e);
        }
    }
    
    /**
     * @description Monitors credential expiry and sends notifications
     */
    @TestVisible
    private void monitorCredentials() {
        // Get expiring credentials
        List<OCH_connection_details__mdt> expiringCredentials = OSB_SRV_OCHCredentialRotation.getExpiringCredentials(WARNING_DAYS);
        
        if (expiringCredentials.isEmpty()) {
            logInfo('No expiring credentials found');
            return;
        }
        
        // Categorize by urgency
        List<OCH_connection_details__mdt> criticalCredentials = new List<OCH_connection_details__mdt>();
        List<OCH_connection_details__mdt> warningCredentials = new List<OCH_connection_details__mdt>();
        
        Date criticalDate = Date.today().addDays(CRITICAL_DAYS);
        
        for (OCH_connection_details__mdt credential : expiringCredentials) {
            if (credential.Credential_Expiry_Date__c <= criticalDate) {
                criticalCredentials.add(credential);
            } else {
                warningCredentials.add(credential);
            }
        }
        
        // Send notifications
        if (!criticalCredentials.isEmpty()) {
            sendCriticalNotification(criticalCredentials);
        }
        
        if (!warningCredentials.isEmpty()) {
            sendWarningNotification(warningCredentials);
        }
        
        // Log monitoring results
        logMonitoringResults(criticalCredentials.size(), warningCredentials.size());
    }
    
    /**
     * @description Sends critical expiry notifications
     * @param credentials List of critically expiring credentials
     */
    private void sendCriticalNotification(List<OCH_connection_details__mdt> credentials) {
        String subject = 'CRITICAL: OCH Credentials Expiring Soon';
        String body = buildNotificationBody(credentials, true);
        
        sendNotification(subject, body, true);
        
        // Log critical credentials
        for (OCH_connection_details__mdt credential : credentials) {
            logCriticalExpiry(credential);
        }
    }
    
    /**
     * @description Sends warning expiry notifications
     * @param credentials List of credentials expiring within warning period
     */
    private void sendWarningNotification(List<OCH_connection_details__mdt> credentials) {
        String subject = 'WARNING: OCH Credentials Expiring';
        String body = buildNotificationBody(credentials, false);
        
        sendNotification(subject, body, false);
    }
    
    /**
     * @description Builds notification email body
     * @param credentials List of expiring credentials
     * @param isCritical Whether this is a critical notification
     * @return Email body content
     */
    private String buildNotificationBody(List<OCH_connection_details__mdt> credentials, Boolean isCritical) {
        String urgency = isCritical ? 'CRITICAL' : 'WARNING';
        String body = urgency + ': The following OCH credentials are expiring soon:\n\n';
        
        for (OCH_connection_details__mdt credential : credentials) {
            body += 'Connection: ' + credential.DeveloperName + '\n';
            body += 'Label: ' + credential.MasterLabel + '\n';
            body += 'Expiry Date: ' + credential.Credential_Expiry_Date__c + '\n';
            body += 'Current Version: ' + credential.Credential_Version__c + '\n';
            body += 'Days Until Expiry: ' + getDaysUntilExpiry(credential.Credential_Expiry_Date__c) + '\n';
            body += '---\n';
        }
        
        body += '\nPlease rotate these credentials as soon as possible.\n';
        body += 'Use the OCH Credential Rotation flow or contact your system administrator.\n\n';
        body += 'This is an automated notification from the OCH Credential Monitoring system.';
        
        return body;
    }
    
    /**
     * @description Sends notification email
     * @param subject Email subject
     * @param body Email body
     * @param isCritical Whether this is critical
     */
    private void sendNotification(String subject, String body, Boolean isCritical) {
        try {
            // Get notification recipients from custom setting or metadata
            List<String> recipients = getNotificationRecipients();
            
            if (recipients.isEmpty()) {
                logError('sendNotification', new System.CalloutException('No notification recipients configured'));
                return;
            }
            
            // Create and send email
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(recipients);
            email.setSubject(subject);
            email.setPlainTextBody(body);
            email.setSaveAsActivity(false);
            
            if (isCritical) {
                email.setImportance(Messaging.EmailImportance.HIGH);
            }
            
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] { email });
            
            logInfo('Notification sent: ' + subject + ' to ' + recipients.size() + ' recipients');
            
        } catch (Exception e) {
            logError('sendNotification', e);
        }
    }
    
    /**
     * @description Gets notification recipients from configuration
     * @return List of email addresses
     */
    private List<String> getNotificationRecipients() {
        // In a real implementation, this would read from custom settings or metadata
        // For now, return system admin emails
        List<String> recipients = new List<String>();
        
        try {
            for (User admin : [SELECT Email FROM User WHERE Profile.Name = 'System Administrator' AND IsActive = true LIMIT 5]) {
                if (String.isNotBlank(admin.Email)) {
                    recipients.add(admin.Email);
                }
            }
        } catch (Exception e) {
            logError('getNotificationRecipients', e);
        }
        
        return recipients;
    }
    
    /**
     * @description Calculates days until expiry
     * @param expiryDate The expiry date
     * @return Number of days until expiry
     */
    private Integer getDaysUntilExpiry(Date expiryDate) {
        if (expiryDate == null) {
            return -1;
        }
        
        return Date.today().daysBetween(expiryDate);
    }
    
    /**
     * @description Logs critical credential expiry
     * @param credential The expiring credential
     */
    private void logCriticalExpiry(OCH_connection_details__mdt credential) {
        try {
            Log__c logEntry = new Log__c(
                Source__c = LOG_SOURCE,
                Area__c = LOG_AREA,
                Type__c = 'Error',
                Message__c = 'CRITICAL: OCH Credential expiring soon\n' +
                           'Connection: ' + credential.DeveloperName + '\n' +
                           'Expiry Date: ' + credential.Credential_Expiry_Date__c + '\n' +
                           'Days Until Expiry: ' + getDaysUntilExpiry(credential.Credential_Expiry_Date__c),
                Context_User__c = UserInfo.getUserId()
            );
            
            insert logEntry;
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to log critical expiry: ' + e.getMessage());
        }
    }
    
    /**
     * @description Logs monitoring results
     * @param criticalCount Number of critical credentials
     * @param warningCount Number of warning credentials
     */
    private void logMonitoringResults(Integer criticalCount, Integer warningCount) {
        try {
            Log__c logEntry = new Log__c(
                Source__c = LOG_SOURCE,
                Area__c = LOG_AREA,
                Type__c = criticalCount > 0 ? 'Error' : 'Info',
                Message__c = 'OCH Credential Monitoring Results:\n' +
                           'Critical Credentials: ' + criticalCount + '\n' +
                           'Warning Credentials: ' + warningCount + '\n' +
                           'Total Monitored: ' + (criticalCount + warningCount),
                Context_User__c = UserInfo.getUserId()
            );
            
            insert logEntry;
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to log monitoring results: ' + e.getMessage());
        }
    }
    
    /**
     * @description Logs informational messages
     * @param message The message to log
     */
    private void logInfo(String message) {
        try {
            Log__c logEntry = new Log__c(
                Source__c = LOG_SOURCE,
                Area__c = LOG_AREA,
                Type__c = 'Info',
                Message__c = message,
                Context_User__c = UserInfo.getUserId()
            );
            
            insert logEntry;
        } catch (Exception e) {
            System.debug(LoggingLevel.INFO, 'Failed to log info: ' + e.getMessage());
        }
    }
    
    /**
     * @description Logs errors
     * @param methodName The method where error occurred
     * @param ex The exception
     */
    private void logError(String methodName, Exception ex) {
        try {
            Log__c logEntry = new Log__c(
                Source__c = LOG_SOURCE + '.' + methodName,
                Area__c = LOG_AREA,
                Type__c = 'Error',
                Message__c = 'Error in credential monitoring:\nError: ' + ex.getMessage() + '\nStack: ' + ex.getStackTraceString(),
                Context_User__c = UserInfo.getUserId()
            );
            
            insert logEntry;
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to log error: ' + e.getMessage());
        }
    }
    
    /**
     * @description Schedules the credential monitor job
     * @param cronExpression Cron expression for scheduling (default: daily at 6 AM)
     * @return Scheduled job ID
     */
    public static String scheduleJob(String cronExpression) {
        if (String.isBlank(cronExpression)) {
            cronExpression = '0 0 6 * * ?'; // Daily at 6 AM
        }
        
        OSB_SCH_OCHCredentialMonitor monitor = new OSB_SCH_OCHCredentialMonitor();
        return System.schedule('OCH Credential Monitor', cronExpression, monitor);
    }
}
