/**
 * @description Helper class for securely accessing OCH credentials through Named Credentials
 * Provides secure credential management for OCH API authentication
 * 
 * <AUTHOR> Agent
 * @date 2025
 */
public inherited sharing class OSB_SRV_OCHCredentialHelper {
    
    private static final String NAMED_CREDENTIAL_PREFIX = 'callout:';
    private static final String OCH_API_PREFIX = 'OCH_API_';
    
    /**
     * @description Gets the Named Credential name for a specific country
     * @param countryName The country name for the OCH connection
     * @return String The Named Credential name to use for API calls
     */
    public static String getNamedCredentialName(String countryName) {
        if (String.isBlank(countryName)) {
            return NAMED_CREDENTIAL_PREFIX + 'OCH_API';
        }
        
        String formattedCountry = countryName.replace(' ', '_');
        return NAMED_CREDENTIAL_PREFIX + OCH_API_PREFIX + formattedCountry;
    }
    
    /**
     * @description Creates an HTTP request with Named Credential authentication
     * @param endpoint The API endpoint path
     * @param method The HTTP method (GET, POST, etc.)
     * @param countryName The country name for credential selection
     * @return HttpRequest The configured HTTP request with authentication
     */
    public static HttpRequest createAuthenticatedRequest(String endpoint, String method, String countryName) {
        HttpRequest request = new HttpRequest();
        
        String namedCredentialUrl = getNamedCredentialName(countryName);
        if (!endpoint.startsWith('/')) {
            endpoint = '/' + endpoint;
        }
        
        request.setEndpoint(namedCredentialUrl + endpoint);
        request.setMethod(method);
        request.setHeader('Content-Type', 'application/json');
        request.setHeader('Accept', 'application/json');
        
        return request;
    }
    
    /**
     * @description Creates an OAuth authentication request for OCH
     * @param connectionDetails The OCH connection metadata
     * @return HttpRequest The configured OAuth request
     */
    public static HttpRequest createOAuthRequest(OCH_connection_details__mdt connectionDetails) {
        String namedCredentialUrl = connectionDetails.OCH_Named_Credential__c;
        if (String.isBlank(namedCredentialUrl)) {
            namedCredentialUrl = connectionDetails.Named_Credential__c;
        }
        
        HttpRequest request = new HttpRequest();
        request.setEndpoint(namedCredentialUrl + connectionDetails.Auth_Path__c);
        request.setMethod('POST');
        request.setHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        return request;
    }
    
    /**
     * @description Creates an AWS statement authentication request
     * @param connectionDetails The OCH connection metadata
     * @return HttpRequest The configured AWS authentication request
     */
    public static HttpRequest createAWSAuthRequest(OCH_connection_details__mdt connectionDetails) {
        String namedCredentialUrl = connectionDetails.AWS_Named_Credential__c;
        if (String.isBlank(namedCredentialUrl)) {
            namedCredentialUrl = connectionDetails.OCH_Named_Credential__c;
        }
        
        HttpRequest request = new HttpRequest();
        request.setEndpoint(namedCredentialUrl + connectionDetails.AWS_STatement_Auth_Path__c);
        request.setMethod('POST');
        request.setHeader('Content-Type', 'application/json');
        
        return request;
    }
    
    /**
     * @description Validates that required Named Credentials are configured
     * @param connectionDetails The OCH connection metadata
     * @return Boolean True if credentials are properly configured
     */
    public static Boolean validateCredentialConfiguration(OCH_connection_details__mdt connectionDetails) {
        return String.isNotBlank(connectionDetails.OCH_Named_Credential__c) || 
               String.isNotBlank(connectionDetails.Named_Credential__c);
    }
}
