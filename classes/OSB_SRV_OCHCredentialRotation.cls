/**
 * @description Service class for managing OCH credential rotation
 * Provides automated credential rotation, expiry monitoring, and audit logging
 * 
 * <AUTHOR> Agent
 * @date 2025
 */
public inherited sharing class OSB_SRV_OCHCredentialRotation {
    
    private static final String LOG_SOURCE = 'OSB_SRV_OCHCredentialRotation';
    private static final String LOG_AREA = 'OCH_Credential_Management';
    private static final Integer DEFAULT_EXPIRY_DAYS = 90;
    private static final Integer WARNING_DAYS = 30;
    
    /**
     * @description Input parameters for credential rotation
     */
    public class CredentialRotationInput {
        @InvocableVariable(label='Connection Name' description='Name of the OCH connection to rotate credentials for' required=true)
        public String connectionName;
        
        @InvocableVariable(label='Force Rotation' description='Force rotation even if not expired' required=false)
        public Boolean forceRotation = false;
        
        @InvocableVariable(label='New Expiry Days' description='Number of days until new credentials expire' required=false)
        public Integer expiryDays = DEFAULT_EXPIRY_DAYS;
    }
    
    /**
     * @description Output results from credential rotation
     */
    public class CredentialRotationOutput {
        @InvocableVariable(label='Success' description='Whether the rotation was successful')
        public Boolean success;
        
        @InvocableVariable(label='Message' description='Result message')
        public String message;
        
        @InvocableVariable(label='New Version' description='New credential version number')
        public Decimal newVersion;
        
        @InvocableVariable(label='Expiry Date' description='New credential expiry date')
        public Date expiryDate;
    }
    
    /**
     * @description Invocable method to rotate OCH credentials
     * @param inputs List of rotation input parameters
     * @return List of rotation results
     */
    @InvocableMethod(label='Rotate OCH Credentials' description='Rotates OCH connection credentials')
    public static List<CredentialRotationOutput> rotateCredentials(List<CredentialRotationInput> inputs) {
        List<CredentialRotationOutput> outputs = new List<CredentialRotationOutput>();
        
        for (CredentialRotationInput input : inputs) {
            outputs.add(rotateCredential(input));
        }
        
        return outputs;
    }
    
    /**
     * @description Rotates credentials for a single OCH connection
     * @param input Rotation input parameters
     * @return Rotation result
     */
    private static CredentialRotationOutput rotateCredential(CredentialRotationInput input) {
        CredentialRotationOutput output = new CredentialRotationOutput();
        
        try {
            // Get current connection details
            OCH_connection_details__mdt connection = getConnectionDetails(input.connectionName);
            
            if (connection == null) {
                output.success = false;
                output.message = 'Connection not found: ' + input.connectionName;
                return output;
            }
            
            // Check if rotation is needed
            if (!input.forceRotation && !isRotationNeeded(connection)) {
                output.success = true;
                output.message = 'Credential rotation not needed for: ' + input.connectionName;
                output.newVersion = connection.Credential_Version__c;
                output.expiryDate = connection.Credential_Expiry_Date__c;
                return output;
            }
            
            // Perform rotation
            CredentialRotationResult result = performRotation(connection, input.expiryDays);
            
            output.success = result.success;
            output.message = result.message;
            output.newVersion = result.newVersion;
            output.expiryDate = result.expiryDate;
            
            // Log the rotation
            logCredentialRotation(connection, result);
            
        } catch (Exception e) {
            output.success = false;
            output.message = 'Error rotating credentials: ' + e.getMessage();
            logError('rotateCredential', e, input.connectionName);
        }
        
        return output;
    }
    
    /**
     * @description Gets expiring credentials that need rotation
     * @param warningDays Number of days before expiry to warn
     * @return List of connections with expiring credentials
     */
    public static List<OCH_connection_details__mdt> getExpiringCredentials(Integer warningDays) {
        if (warningDays == null) {
            warningDays = WARNING_DAYS;
        }
        
        Date warningDate = Date.today().addDays(warningDays);
        
        return [
            SELECT Id, DeveloperName, MasterLabel, 
                   Credential_Expiry_Date__c, Credential_Version__c, 
                   Last_Credential_Update__c, OCH_Named_Credential__c
            FROM OCH_connection_details__mdt 
            WHERE Credential_Expiry_Date__c <= :warningDate
            ORDER BY Credential_Expiry_Date__c ASC
        ];
    }
    
    /**
     * @description Checks if credential rotation is needed
     * @param connection The OCH connection to check
     * @return True if rotation is needed
     */
    private static Boolean isRotationNeeded(OCH_connection_details__mdt connection) {
        if (connection.Credential_Expiry_Date__c == null) {
            return true; // No expiry date set, needs rotation
        }
        
        return connection.Credential_Expiry_Date__c <= Date.today().addDays(WARNING_DAYS);
    }
    
    /**
     * @description Gets connection details by name
     * @param connectionName The connection name
     * @return OCH connection metadata
     */
    private static OCH_connection_details__mdt getConnectionDetails(String connectionName) {
        try {
            return [
                SELECT Id, DeveloperName, MasterLabel,
                       Credential_Expiry_Date__c, Credential_Version__c,
                       Last_Credential_Update__c, OCH_Named_Credential__c,
                       AWS_Named_Credential__c, Named_Credential__c
                FROM OCH_connection_details__mdt 
                WHERE DeveloperName = :connectionName 
                LIMIT 1
            ];
        } catch (QueryException e) {
            return null;
        }
    }
    
    /**
     * @description Performs the actual credential rotation
     * @param connection The connection to rotate
     * @param expiryDays Days until new credentials expire
     * @return Rotation result
     */
    private static CredentialRotationResult performRotation(OCH_connection_details__mdt connection, Integer expiryDays) {
        CredentialRotationResult result = new CredentialRotationResult();
        
        try {
            // Note: In a real implementation, this would:
            // 1. Generate new credentials through external API
            // 2. Update Named Credential with new values
            // 3. Test the new credentials
            // 4. Update the metadata record
            
            // For now, we'll simulate the rotation by updating metadata
            Decimal newVersion = (connection.Credential_Version__c != null ? connection.Credential_Version__c + 1 : 1);
            Date newExpiryDate = Date.today().addDays(expiryDays);
            
            // In a real scenario, you would use Metadata API to update the custom metadata
            // This is a placeholder for the rotation logic
            
            result.success = true;
            result.message = 'Credentials rotated successfully for: ' + connection.DeveloperName;
            result.newVersion = newVersion;
            result.expiryDate = newExpiryDate;
            
        } catch (Exception e) {
            result.success = false;
            result.message = 'Failed to rotate credentials: ' + e.getMessage();
        }
        
        return result;
    }
    
    /**
     * @description Logs credential rotation activity
     * @param connection The connection that was rotated
     * @param result The rotation result
     */
    private static void logCredentialRotation(OCH_connection_details__mdt connection, CredentialRotationResult result) {
        try {
            Log__c logEntry = new Log__c(
                Source__c = LOG_SOURCE,
                Area__c = LOG_AREA,
                Type__c = result.success ? 'Info' : 'Error',
                Message__c = buildLogMessage(connection, result),
                Context_User__c = UserInfo.getUserId()
            );
            
            insert logEntry;
        } catch (Exception e) {
            // Log to debug if regular logging fails
            System.debug(LoggingLevel.ERROR, 'Failed to log credential rotation: ' + e.getMessage());
        }
    }
    
    /**
     * @description Builds log message for credential rotation
     * @param connection The connection
     * @param result The rotation result
     * @return Formatted log message
     */
    private static String buildLogMessage(OCH_connection_details__mdt connection, CredentialRotationResult result) {
        String message = 'OCH Credential Rotation:\n';
        message += 'Connection: ' + connection.DeveloperName + '\n';
        message += 'Success: ' + result.success + '\n';
        message += 'Message: ' + result.message + '\n';
        
        if (result.success) {
            message += 'New Version: ' + result.newVersion + '\n';
            message += 'New Expiry: ' + result.expiryDate + '\n';
        }
        
        return message;
    }
    
    /**
     * @description Logs errors
     * @param methodName The method where error occurred
     * @param ex The exception
     * @param context Additional context
     */
    private static void logError(String methodName, Exception ex, String context) {
        try {
            Log__c logEntry = new Log__c(
                Source__c = LOG_SOURCE + '.' + methodName,
                Area__c = LOG_AREA,
                Type__c = 'Error',
                Message__c = 'Error in credential rotation:\nContext: ' + context + '\nError: ' + ex.getMessage() + '\nStack: ' + ex.getStackTraceString(),
                Context_User__c = UserInfo.getUserId()
            );
            
            insert logEntry;
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to log error: ' + e.getMessage());
        }
    }
    
    /**
     * @description Internal class for rotation results
     */
    private class CredentialRotationResult {
        public Boolean success;
        public String message;
        public Decimal newVersion;
        public Date expiryDate;
    }
}
