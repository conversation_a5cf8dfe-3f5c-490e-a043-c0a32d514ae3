/**
 * @description Audit logging service for OCH credential access and operations
 * Provides comprehensive audit trail for security monitoring and compliance
 * 
 * <AUTHOR> Agent
 * @date 2025
 */
public inherited sharing class OSB_SRV_OCHAuditLogger {
    
    private static final String LOG_SOURCE = 'OSB_SRV_OCHAuditLogger';
    private static final String LOG_AREA = 'OCH_Security_Audit';
    
    /**
     * @description Audit event types for OCH operations
     */
    public enum AuditEventType {
        CREDENTIAL_ACCESS,
        AUTHENTICATION_REQUEST,
        AUTHENTICATION_SUCCESS,
        AUTHENTICATION_FAILURE,
        CREDENTIAL_ROTATION,
        CONFIGURATION_CHANGE,
        SECURITY_VIOLATION,
        API_CALL_SUCCESS,
        API_CALL_FAILURE
    }
    
    /**
     * @description Logs credential access events
     * @param connectionName Name of the OCH connection
     * @param credentialType Type of credential accessed
     * @param accessReason Reason for accessing the credential
     */
    public static void logCredentialAccess(String connectionName, String credentialType, String accessReason) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.CREDENTIAL_ACCESS;
        entry.connectionName = connectionName;
        entry.credentialType = credentialType;
        entry.description = 'Credential accessed: ' + credentialType + ' for connection: ' + connectionName;
        entry.reason = accessReason;
        entry.severity = 'Medium';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs authentication request events
     * @param connectionName Name of the OCH connection
     * @param endpoint API endpoint being called
     * @param requestId Unique request identifier
     */
    public static void logAuthenticationRequest(String connectionName, String endpoint, String requestId) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.AUTHENTICATION_REQUEST;
        entry.connectionName = connectionName;
        entry.endpoint = endpoint;
        entry.requestId = requestId;
        entry.description = 'Authentication request initiated for: ' + connectionName;
        entry.severity = 'Low';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs successful authentication events
     * @param connectionName Name of the OCH connection
     * @param endpoint API endpoint
     * @param requestId Unique request identifier
     * @param responseTime Response time in milliseconds
     */
    public static void logAuthenticationSuccess(String connectionName, String endpoint, String requestId, Long responseTime) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.AUTHENTICATION_SUCCESS;
        entry.connectionName = connectionName;
        entry.endpoint = endpoint;
        entry.requestId = requestId;
        entry.responseTime = responseTime;
        entry.description = 'Authentication successful for: ' + connectionName;
        entry.severity = 'Low';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs failed authentication events
     * @param connectionName Name of the OCH connection
     * @param endpoint API endpoint
     * @param requestId Unique request identifier
     * @param errorCode Error code from response
     * @param errorMessage Error message
     */
    public static void logAuthenticationFailure(String connectionName, String endpoint, String requestId, String errorCode, String errorMessage) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.AUTHENTICATION_FAILURE;
        entry.connectionName = connectionName;
        entry.endpoint = endpoint;
        entry.requestId = requestId;
        entry.errorCode = errorCode;
        entry.errorMessage = errorMessage;
        entry.description = 'Authentication failed for: ' + connectionName + ' - ' + errorMessage;
        entry.severity = 'High';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs credential rotation events
     * @param connectionName Name of the OCH connection
     * @param oldVersion Previous credential version
     * @param newVersion New credential version
     * @param rotationReason Reason for rotation
     */
    public static void logCredentialRotation(String connectionName, Decimal oldVersion, Decimal newVersion, String rotationReason) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.CREDENTIAL_ROTATION;
        entry.connectionName = connectionName;
        entry.oldVersion = oldVersion;
        entry.newVersion = newVersion;
        entry.reason = rotationReason;
        entry.description = 'Credentials rotated for: ' + connectionName + ' from v' + oldVersion + ' to v' + newVersion;
        entry.severity = 'Medium';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs configuration change events
     * @param connectionName Name of the OCH connection
     * @param fieldChanged Field that was changed
     * @param oldValue Previous value (masked if sensitive)
     * @param newValue New value (masked if sensitive)
     */
    public static void logConfigurationChange(String connectionName, String fieldChanged, String oldValue, String newValue) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.CONFIGURATION_CHANGE;
        entry.connectionName = connectionName;
        entry.fieldChanged = fieldChanged;
        entry.oldValue = maskSensitiveValue(fieldChanged, oldValue);
        entry.newValue = maskSensitiveValue(fieldChanged, newValue);
        entry.description = 'Configuration changed for: ' + connectionName + ' - ' + fieldChanged;
        entry.severity = 'Medium';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs security violation events
     * @param connectionName Name of the OCH connection
     * @param violationType Type of security violation
     * @param violationDetails Details of the violation
     */
    public static void logSecurityViolation(String connectionName, String violationType, String violationDetails) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.SECURITY_VIOLATION;
        entry.connectionName = connectionName;
        entry.violationType = violationType;
        entry.violationDetails = violationDetails;
        entry.description = 'Security violation detected: ' + violationType + ' for connection: ' + connectionName;
        entry.severity = 'Critical';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs successful API call events
     * @param connectionName Name of the OCH connection
     * @param endpoint API endpoint
     * @param requestId Unique request identifier
     * @param responseTime Response time in milliseconds
     */
    public static void logApiCallSuccess(String connectionName, String endpoint, String requestId, Long responseTime) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.API_CALL_SUCCESS;
        entry.connectionName = connectionName;
        entry.endpoint = endpoint;
        entry.requestId = requestId;
        entry.responseTime = responseTime;
        entry.description = 'API call successful: ' + endpoint + ' for connection: ' + connectionName;
        entry.severity = 'Low';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Logs failed API call events
     * @param connectionName Name of the OCH connection
     * @param endpoint API endpoint
     * @param requestId Unique request identifier
     * @param errorCode Error code from response
     * @param errorMessage Error message
     */
    public static void logApiCallFailure(String connectionName, String endpoint, String requestId, String errorCode, String errorMessage) {
        AuditLogEntry entry = new AuditLogEntry();
        entry.eventType = AuditEventType.API_CALL_FAILURE;
        entry.connectionName = connectionName;
        entry.endpoint = endpoint;
        entry.requestId = requestId;
        entry.errorCode = errorCode;
        entry.errorMessage = errorMessage;
        entry.description = 'API call failed: ' + endpoint + ' for connection: ' + connectionName + ' - ' + errorMessage;
        entry.severity = 'High';
        
        logAuditEvent(entry);
    }
    
    /**
     * @description Creates and inserts audit log entry
     * @param entry The audit log entry to create
     */
    private static void logAuditEvent(AuditLogEntry entry) {
        try {
            Log__c logRecord = new Log__c();
            logRecord.Source__c = LOG_SOURCE;
            logRecord.Area__c = LOG_AREA;
            logRecord.Type__c = mapSeverityToLogType(entry.severity);
            logRecord.Message__c = buildAuditMessage(entry);
            logRecord.Context_User__c = UserInfo.getUserId();
            
            insert logRecord;
            
            // For critical events, also create platform events for real-time monitoring
            if (entry.severity == 'Critical') {
                publishSecurityEvent(entry);
            }
            
        } catch (Exception e) {
            // Fallback to debug logging if audit logging fails
            System.debug(LoggingLevel.ERROR, 'Failed to create audit log: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, 'Audit entry: ' + JSON.serialize(entry));
        }
    }
    
    /**
     * @description Builds comprehensive audit message
     * @param entry The audit log entry
     * @return Formatted audit message
     */
    private static String buildAuditMessage(AuditLogEntry entry) {
        String message = 'OCH SECURITY AUDIT\n';
        message += '==================\n';
        message += 'Event Type: ' + entry.eventType + '\n';
        message += 'Timestamp: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss') + '\n';
        message += 'User: ' + UserInfo.getName() + ' (' + UserInfo.getUserId() + ')\n';
        message += 'Session: ' + UserInfo.getSessionId().substring(0, 15) + '...\n';
        message += 'IP Address: ' + getClientIpAddress() + '\n';
        message += 'User Agent: ' + getClientUserAgent() + '\n';
        message += 'Connection: ' + (entry.connectionName != null ? entry.connectionName : 'N/A') + '\n';
        message += 'Description: ' + entry.description + '\n';
        message += 'Severity: ' + entry.severity + '\n';
        
        if (String.isNotBlank(entry.endpoint)) {
            message += 'Endpoint: ' + entry.endpoint + '\n';
        }
        
        if (String.isNotBlank(entry.requestId)) {
            message += 'Request ID: ' + entry.requestId + '\n';
        }
        
        if (entry.responseTime != null) {
            message += 'Response Time: ' + entry.responseTime + 'ms\n';
        }
        
        if (String.isNotBlank(entry.errorCode)) {
            message += 'Error Code: ' + entry.errorCode + '\n';
        }
        
        if (String.isNotBlank(entry.errorMessage)) {
            message += 'Error Message: ' + entry.errorMessage + '\n';
        }
        
        if (String.isNotBlank(entry.reason)) {
            message += 'Reason: ' + entry.reason + '\n';
        }
        
        if (entry.oldVersion != null || entry.newVersion != null) {
            message += 'Version Change: ' + entry.oldVersion + ' → ' + entry.newVersion + '\n';
        }
        
        if (String.isNotBlank(entry.fieldChanged)) {
            message += 'Field Changed: ' + entry.fieldChanged + '\n';
            message += 'Old Value: ' + entry.oldValue + '\n';
            message += 'New Value: ' + entry.newValue + '\n';
        }
        
        if (String.isNotBlank(entry.violationType)) {
            message += 'Violation Type: ' + entry.violationType + '\n';
            message += 'Violation Details: ' + entry.violationDetails + '\n';
        }
        
        return message;
    }
    
    /**
     * @description Maps severity to log type
     * @param severity The severity level
     * @return Log type
     */
    private static String mapSeverityToLogType(String severity) {
        switch on severity {
            when 'Critical' {
                return 'Error';
            }
            when 'High' {
                return 'Error';
            }
            when 'Medium' {
                return 'Warning';
            }
            when else {
                return 'Info';
            }
        }
    }
    
    /**
     * @description Masks sensitive values in audit logs
     * @param fieldName The field name
     * @param value The value to potentially mask
     * @return Masked or original value
     */
    private static String maskSensitiveValue(String fieldName, String value) {
        if (String.isBlank(value)) {
            return value;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        if (lowerFieldName.contains('password') || 
            lowerFieldName.contains('secret') || 
            lowerFieldName.contains('key') || 
            lowerFieldName.contains('token')) {
            return '***MASKED***';
        }
        
        return value;
    }
    
    /**
     * @description Gets client IP address from request headers
     * @return Client IP address
     */
    private static String getClientIpAddress() {
        try {
            return System.getApplicationReadWriteMode() == ApplicationReadWriteMode.READ_WRITE ? 
                   Auth.SessionManagement.getCurrentSession().get('SourceIp') : 'Unknown';
        } catch (Exception e) {
            return 'Unknown';
        }
    }
    
    /**
     * @description Gets client user agent from request headers
     * @return Client user agent
     */
    private static String getClientUserAgent() {
        try {
            return System.getApplicationReadWriteMode() == ApplicationReadWriteMode.READ_WRITE ? 
                   Auth.SessionManagement.getCurrentSession().get('LoginType') : 'Unknown';
        } catch (Exception e) {
            return 'Unknown';
        }
    }
    
    /**
     * @description Publishes security event for real-time monitoring
     * @param entry The audit log entry
     */
    private static void publishSecurityEvent(AuditLogEntry entry) {
        try {
            // In a real implementation, this would publish a Platform Event
            // for real-time security monitoring and alerting
            System.debug(LoggingLevel.ERROR, 'CRITICAL SECURITY EVENT: ' + entry.description);
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to publish security event: ' + e.getMessage());
        }
    }
    
    /**
     * @description Internal class for audit log entries
     */
    private class AuditLogEntry {
        public AuditEventType eventType;
        public String connectionName;
        public String credentialType;
        public String endpoint;
        public String requestId;
        public Long responseTime;
        public String errorCode;
        public String errorMessage;
        public String reason;
        public Decimal oldVersion;
        public Decimal newVersion;
        public String fieldChanged;
        public String oldValue;
        public String newValue;
        public String violationType;
        public String violationDetails;
        public String description;
        public String severity;
    }
}
