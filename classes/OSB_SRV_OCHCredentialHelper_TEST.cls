/**
 * @description Test class for OSB_SRV_OCHCredentialHelper
 * Tests secure credential management and audit logging functionality
 * 
 * <AUTHOR> Agent
 * @date 2025
 */
@IsTest
private class OSB_SRV_OCHCredentialHelper_TEST {
    
    /**
     * @description Test data setup
     */
    @TestSetup
    static void setupTestData() {
        // Create test log records will be created during test execution
        // No specific setup needed as we're testing utility methods
    }
    
    /**
     * @description Test getNamedCredentialName method
     */
    @IsTest
    static void testGetNamedCredentialName() {
        Test.startTest();
        
        // Test with country name
        String result1 = OSB_SRV_OCHCredentialHelper.getNamedCredentialName('Zambia');
        System.assertEquals('callout:OCH_API_Zambia', result1, 'Should return country-specific Named Credential');
        
        // Test with country name containing spaces
        String result2 = OSB_SRV_OCHCredentialHelper.getNamedCredentialName('South Africa');
        System.assertEquals('callout:OCH_API_South_Africa', result2, 'Should replace spaces with underscores');
        
        // Test with blank country name
        String result3 = OSB_SRV_OCHCredentialHelper.getNamedCredentialName('');
        System.assertEquals('callout:OCH_API', result3, 'Should return default Named Credential for blank input');
        
        // Test with null country name
        String result4 = OSB_SRV_OCHCredentialHelper.getNamedCredentialName(null);
        System.assertEquals('callout:OCH_API', result4, 'Should return default Named Credential for null input');
        
        Test.stopTest();
    }
    
    /**
     * @description Test createAuthenticatedRequest method
     */
    @IsTest
    static void testCreateAuthenticatedRequest() {
        Test.startTest();
        
        // Test basic request creation
        HttpRequest request1 = OSB_SRV_OCHCredentialHelper.createAuthenticatedRequest('/api/test', 'GET', 'Zambia');
        
        System.assertEquals('GET', request1.getMethod(), 'Should set correct HTTP method');
        System.assert(request1.getEndpoint().contains('callout:OCH_API_Zambia'), 'Should use correct Named Credential');
        System.assert(request1.getEndpoint().endsWith('/api/test'), 'Should append endpoint path');
        System.assertEquals('application/json', request1.getHeader('Content-Type'), 'Should set Content-Type header');
        System.assertEquals('application/json', request1.getHeader('Accept'), 'Should set Accept header');
        System.assertNotEquals(null, request1.getHeader('X-Request-ID'), 'Should set request ID header');
        
        // Test endpoint without leading slash
        HttpRequest request2 = OSB_SRV_OCHCredentialHelper.createAuthenticatedRequest('api/test', 'POST', 'Mauritius');
        System.assert(request2.getEndpoint().endsWith('/api/test'), 'Should add leading slash to endpoint');
        
        Test.stopTest();
        
        // Verify audit logs were created
        List<Log__c> auditLogs = [SELECT Id, Type__c, Message__c FROM Log__c WHERE Source__c = 'OSB_SRV_OCHAuditLogger'];
        System.assert(auditLogs.size() > 0, 'Should create audit log entries');
    }
    
    /**
     * @description Test createOAuthRequest method
     */
    @IsTest
    static void testCreateOAuthRequest() {
        Test.startTest();
        
        // Create mock connection details
        OCH_connection_details__mdt mockConnection = new OCH_connection_details__mdt();
        mockConnection.DeveloperName = 'Test_Connection';
        mockConnection.OCH_Named_Credential__c = 'callout:OCH_API_Test';
        mockConnection.Auth_Path__c = '/oauth/token';
        
        HttpRequest request = OSB_SRV_OCHCredentialHelper.createOAuthRequest(mockConnection);
        
        System.assertEquals('POST', request.getMethod(), 'Should set POST method for OAuth');
        System.assertEquals('callout:OCH_API_Test/oauth/token', request.getEndpoint(), 'Should use OCH Named Credential');
        System.assertEquals('application/x-www-form-urlencoded', request.getHeader('Content-Type'), 'Should set form-encoded content type');
        System.assertNotEquals(null, request.getHeader('X-Request-ID'), 'Should set request ID header');
        
        Test.stopTest();
        
        // Verify audit logs were created
        List<Log__c> auditLogs = [SELECT Id, Type__c, Message__c FROM Log__c WHERE Source__c = 'OSB_SRV_OCHAuditLogger'];
        System.assert(auditLogs.size() > 0, 'Should create audit log entries for OAuth request');
    }
    
    /**
     * @description Test createOAuthRequest with fallback credential
     */
    @IsTest
    static void testCreateOAuthRequestWithFallback() {
        Test.startTest();
        
        // Create mock connection details without OCH Named Credential
        OCH_connection_details__mdt mockConnection = new OCH_connection_details__mdt();
        mockConnection.DeveloperName = 'Test_Connection';
        mockConnection.Named_Credential__c = 'callout:OCH_API_Fallback';
        mockConnection.Auth_Path__c = '/oauth/token';
        
        HttpRequest request = OSB_SRV_OCHCredentialHelper.createOAuthRequest(mockConnection);
        
        System.assertEquals('callout:OCH_API_Fallback/oauth/token', request.getEndpoint(), 'Should use fallback Named Credential');
        
        Test.stopTest();
    }
    
    /**
     * @description Test createAWSAuthRequest method
     */
    @IsTest
    static void testCreateAWSAuthRequest() {
        Test.startTest();
        
        // Create mock connection details
        OCH_connection_details__mdt mockConnection = new OCH_connection_details__mdt();
        mockConnection.DeveloperName = 'Test_Connection';
        mockConnection.AWS_Named_Credential__c = 'callout:OCH_API_AWS';
        mockConnection.AWS_STatement_Auth_Path__c = '/aws/auth';
        
        HttpRequest request = OSB_SRV_OCHCredentialHelper.createAWSAuthRequest(mockConnection);
        
        System.assertEquals('POST', request.getMethod(), 'Should set POST method for AWS auth');
        System.assertEquals('callout:OCH_API_AWS/aws/auth', request.getEndpoint(), 'Should use AWS Named Credential');
        System.assertEquals('application/json', request.getHeader('Content-Type'), 'Should set JSON content type');
        
        Test.stopTest();
    }
    
    /**
     * @description Test validateCredentialConfiguration method
     */
    @IsTest
    static void testValidateCredentialConfiguration() {
        Test.startTest();
        
        // Test valid configuration with OCH Named Credential
        OCH_connection_details__mdt validConnection1 = new OCH_connection_details__mdt();
        validConnection1.DeveloperName = 'Valid_Connection_1';
        validConnection1.OCH_Named_Credential__c = 'callout:OCH_API_Test';
        
        Boolean result1 = OSB_SRV_OCHCredentialHelper.validateCredentialConfiguration(validConnection1);
        System.assertEquals(true, result1, 'Should return true for valid OCH Named Credential');
        
        // Test valid configuration with fallback Named Credential
        OCH_connection_details__mdt validConnection2 = new OCH_connection_details__mdt();
        validConnection2.DeveloperName = 'Valid_Connection_2';
        validConnection2.Named_Credential__c = 'callout:OCH_API_Fallback';
        
        Boolean result2 = OSB_SRV_OCHCredentialHelper.validateCredentialConfiguration(validConnection2);
        System.assertEquals(true, result2, 'Should return true for valid fallback Named Credential');
        
        // Test invalid configuration
        OCH_connection_details__mdt invalidConnection = new OCH_connection_details__mdt();
        invalidConnection.DeveloperName = 'Invalid_Connection';
        
        Boolean result3 = OSB_SRV_OCHCredentialHelper.validateCredentialConfiguration(invalidConnection);
        System.assertEquals(false, result3, 'Should return false for invalid configuration');
        
        Test.stopTest();
        
        // Verify security violation was logged for invalid configuration
        List<Log__c> securityLogs = [SELECT Id, Type__c, Message__c FROM Log__c WHERE Source__c = 'OSB_SRV_OCHAuditLogger' AND Type__c = 'Error'];
        System.assert(securityLogs.size() > 0, 'Should log security violation for invalid configuration');
    }
    
    /**
     * @description Test logApiSuccess method
     */
    @IsTest
    static void testLogApiSuccess() {
        Test.startTest();
        
        OSB_SRV_OCHCredentialHelper.logApiSuccess('Test_Connection', '/api/test', 'REQ_123', 250L);
        
        Test.stopTest();
        
        // Verify audit logs were created
        List<Log__c> auditLogs = [SELECT Id, Type__c, Message__c FROM Log__c WHERE Source__c = 'OSB_SRV_OCHAuditLogger'];
        System.assert(auditLogs.size() > 0, 'Should create audit log entries for API success');
        
        Boolean foundSuccessLog = false;
        for (Log__c log : auditLogs) {
            if (log.Message__c.contains('API call successful')) {
                foundSuccessLog = true;
                break;
            }
        }
        System.assert(foundSuccessLog, 'Should log API success event');
    }
    
    /**
     * @description Test logApiFailure method
     */
    @IsTest
    static void testLogApiFailure() {
        Test.startTest();
        
        OSB_SRV_OCHCredentialHelper.logApiFailure('Test_Connection', '/api/test', 'REQ_123', '401', 'Unauthorized');
        
        Test.stopTest();
        
        // Verify audit logs were created
        List<Log__c> auditLogs = [SELECT Id, Type__c, Message__c FROM Log__c WHERE Source__c = 'OSB_SRV_OCHAuditLogger'];
        System.assert(auditLogs.size() > 0, 'Should create audit log entries for API failure');
        
        Boolean foundFailureLog = false;
        for (Log__c log : auditLogs) {
            if (log.Message__c.contains('API call failed')) {
                foundFailureLog = true;
                break;
            }
        }
        System.assert(foundFailureLog, 'Should log API failure event');
    }
    
    /**
     * @description Test audit logging integration
     */
    @IsTest
    static void testAuditLoggingIntegration() {
        Test.startTest();
        
        // Perform multiple operations that should generate audit logs
        String credentialName = OSB_SRV_OCHCredentialHelper.getNamedCredentialName('TestCountry');
        HttpRequest request = OSB_SRV_OCHCredentialHelper.createAuthenticatedRequest('/test', 'GET', 'TestCountry');
        
        OCH_connection_details__mdt mockConnection = new OCH_connection_details__mdt();
        mockConnection.DeveloperName = 'Test_Connection';
        mockConnection.OCH_Named_Credential__c = 'callout:OCH_API_Test';
        mockConnection.Auth_Path__c = '/oauth/token';
        
        HttpRequest oauthRequest = OSB_SRV_OCHCredentialHelper.createOAuthRequest(mockConnection);
        Boolean isValid = OSB_SRV_OCHCredentialHelper.validateCredentialConfiguration(mockConnection);
        
        Test.stopTest();
        
        // Verify comprehensive audit trail
        List<Log__c> allAuditLogs = [SELECT Id, Type__c, Message__c, Source__c FROM Log__c WHERE Source__c = 'OSB_SRV_OCHAuditLogger'];
        System.assert(allAuditLogs.size() > 0, 'Should create comprehensive audit trail');
        
        // Verify different types of audit events were logged
        Set<String> loggedEvents = new Set<String>();
        for (Log__c log : allAuditLogs) {
            if (log.Message__c.contains('CREDENTIAL_ACCESS')) loggedEvents.add('CREDENTIAL_ACCESS');
            if (log.Message__c.contains('AUTHENTICATION_REQUEST')) loggedEvents.add('AUTHENTICATION_REQUEST');
        }
        
        System.assert(loggedEvents.size() > 0, 'Should log different types of audit events');
    }
}
